use minifb::{Key, Scale, ScaleMode, Window, WindowOptions};
use std::time::Duration;

/// CHIP-8 Display implementation
/// The CHIP-8 has a 64x32 pixel monochrome display
pub struct Display {
    // The actual pixel buffer (64x32)
    pixels: [u32; 64 * 32],
    // Window for rendering
    window: Window,
    // Scale factor for the window (each CHIP-8 pixel will be SCALE x SCALE pixels)
    scale: u32,
    // Background and foreground colors (in ARGB format)
    bg_color: u32,
    fg_color: u32,
}

impl Display {
    /// Creates a new Display with default settings
    pub fn new() -> Result<Self, String> {
        let width = 64;
        let height = 32;
        let scale = 10; // Each CHIP-8 pixel will be 10x10 screen pixels

        let window_options = WindowOptions {
            scale: Scale::X8,  // Start with 8x scale
            scale_mode: ScaleMode::AspectRatioStretch,
            ..WindowOptions::default()
        };

        let window = Window::new(
            "CHIP-8 Emulator",
            (width * scale as usize) as usize,
            (height * scale as usize) as usize,
            window_options,
        )
        .map_err(|e| e.to_string())?;

        Ok(Self {
            pixels: [0; 64 * 32],
            window,
            scale: scale as u32,
            bg_color: 0xFF_00_00_00, // Black
            fg_color: 0xFF_FF_FF_FF, // White
        })
    }


    /// Clears the display (sets all pixels to background color)
    pub fn clear(&mut self) {
        self.pixels = [0; 64 * 32];
    }

    /// Draws a sprite at the given coordinates and returns true if any pixels were flipped from set to unset
    /// This is used for collision detection in CHIP-8
    pub fn draw_sprite(&mut self, x: u8, y: u8, sprite: &[u8]) -> bool {
        let mut collision = false;
        let x = x as usize % 64;
        let y = y as usize % 32;

        for (row, &byte) in sprite.iter().enumerate() {
            let y_pos = (y + row) % 32;

            for col in 0..8 {
                let x_pos = (x + col) % 64;

                // Check each bit in the sprite byte
                if (byte & (0x80 >> col)) != 0 {
                    let idx = y_pos * 64 + x_pos;

                    // XOR the pixel (CHIP-8 uses XOR for drawing)
                    if self.pixels[idx] == self.fg_color {
                        collision = true;
                        self.pixels[idx] = self.bg_color;
                    } else {
                        self.pixels[idx] = self.fg_color;
                    }
                }
            }
        }

        collision
    }

    /// Updates the display window (should be called every frame)
    pub fn update(&mut self) -> bool {
        // Limit to 60 FPS
        std::thread::sleep(Duration::from_millis(16));

        // Update the window with the current pixel buffer
        self.window
            .update_with_buffer(&self.pixels, 64, 32)
            .is_ok()
    }

    /// Checks if the window is still open
    pub fn is_open(&self) -> bool {
        self.window.is_open()
    }

    /// Handles input and returns a 16-element array representing the state of the CHIP-8 keypad
    pub fn get_keys(&self) -> [bool; 16] {
        let mut keys = [false; 16];

        // CHIP-8 keypad mapping to computer keyboard:
        // 1 2 3 C   1 2 3 4
        // 4 5 6 D   Q W E R
        // 7 8 9 E   A S D F
        // A 0 B F   Z X C V
        let key_mapping = [
            Key::X,     // 0
            Key::Key1,  // 1
            Key::Key2,  // 2
            Key::Key3,  // 3
            Key::Q,     // 4
            Key::W,     // 5
            Key::E,     // 6
            Key::A,     // 7
            Key::S,     // 8
            Key::D,     // 9
            Key::Z,     // A
            Key::C,     // B
            Key::Key4,  // C
            Key::R,     // D
            Key::F,     // E
            Key::V,     // F
        ];

        for (i, &key) in key_mapping.iter().enumerate() {
            keys[i] = self.window.is_key_down(key);
        }

        keys
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_display_creation() {
        let display = Display::new();
        assert!(display.is_ok());
    }

    #[test]
    fn test_draw_sprite() {
        let mut display = Display::new().unwrap();
        // Test sprite: a 2x2 square
        let sprite = [0xF0, 0xF0];

        // Draw sprite at (0,0)
        let collision = display.draw_sprite(0, 0, &sprite);
        assert!(!collision, "No collision should occur on first draw");

        // Draw same sprite again - should cause collision
        let collision = display.draw_sprite(0, 0, &sprite);
        assert!(collision, "Collision should be detected when drawing over existing pixels");
    }
}
