use crate::memory::Memory;
use crate::registers::Registers;
use crate::display::Display;
use crate::keyboard::Keyboard;
use std::time::{Duration, Instant};

pub struct Cpu {
    memory: Memory,
    registers: Registers,
    display: Display,
    keyboard: Keyboard,
    stack: [u16; 16],
    last_timer_update: Instant,
}

impl Cpu {
    pub fn new() -> Result<Self, String> {
        Ok(Cpu {
            memory: Memory::new(),
            registers: Registers::new(),
            display: Display::new()?,
            keyboard: Keyboard::new(),
            stack: [0; 16],
            last_timer_update: Instant::now(),
        })
    }

    pub fn load_rom(&mut self, rom_data: &[u8]) {
        self.memory.load_program(rom_data, 0x200);
        self.load_font_set();
    }

    fn load_font_set(&mut self) {
        // CHIP-8 font set (each character is 4x5 pixels)
        let font_set = [
            0xF0, 0x90, 0x90, 0x90, 0xF0, // 0
            0x20, 0x60, 0x20, 0x20, 0x70, // 1
            0xF0, 0x10, 0xF0, 0x80, 0xF0, // 2
            0xF0, 0x10, 0xF0, 0x10, 0xF0, // 3
            0x90, 0x90, 0xF0, 0x10, 0x10, // 4
            0xF0, 0x80, 0xF0, 0x10, 0xF0, // 5
            0xF0, 0x80, 0xF0, 0x90, 0xF0, // 6
            0xF0, 0x10, 0x20, 0x40, 0x40, // 7
            0xF0, 0x90, 0xF0, 0x90, 0xF0, // 8
            0xF0, 0x90, 0xF0, 0x10, 0xF0, // 9
            0xF0, 0x90, 0xF0, 0x90, 0x90, // A
            0xE0, 0x90, 0xE0, 0x90, 0xE0, // B
            0xF0, 0x80, 0x80, 0x80, 0xF0, // C
            0xE0, 0x90, 0x90, 0x90, 0xE0, // D
            0xF0, 0x80, 0xF0, 0x80, 0xF0, // E
            0xF0, 0x80, 0xF0, 0x80, 0x80  // F
        ];

        // Load font set starting at address 0x50
        for (i, &byte) in font_set.iter().enumerate() {
            self.memory.write_byte(0x50 + i as u16, byte);
        }
    }

    pub fn run(&mut self) -> Result<(), String> {
        while self.display.is_open() {
            // Fetch instruction
            let instruction = self.fetch_instruction();

            // Execute instruction
            self.execute_instruction(instruction)?;

            // Update timers at 60Hz
            if self.last_timer_update.elapsed() >= Duration::from_millis(16) {
                self.registers.update_timers();
                self.last_timer_update = Instant::now();
            }

            // Update display
            if !self.display.update() {
                break;
            }

            // Update keyboard state
            self.update_keyboard();
        }
        Ok(())
    }

    fn fetch_instruction(&mut self) -> u16 {
        let high_byte = self.memory.read_byte(self.registers.pc) as u16;
        let low_byte = self.memory.read_byte(self.registers.pc + 1) as u16;
        let instruction = (high_byte << 8) | low_byte;
        self.registers.pc += 2;
        instruction
    }

    fn update_keyboard(&mut self) {
        let keys = self.display.get_keys();
        for (i, &pressed) in keys.iter().enumerate() {
            if pressed {
                self.keyboard.press_key(i);
            } else {
                self.keyboard.release_key(i);
            }
        }
    }

    fn execute_instruction(&mut self, instruction: u16) -> Result<(), String> {
        // Extract instruction components
        let opcode = (instruction & 0xF000) >> 12;
        let x = ((instruction & 0x0F00) >> 8) as usize;
        let y = ((instruction & 0x00F0) >> 4) as usize;
        let n = (instruction & 0x000F) as u8;
        let nn = (instruction & 0x00FF) as u8;
        let nnn = instruction & 0x0FFF;

        match opcode {
            0x0 => {
                match nn {
                    0xE0 => self.display.clear(), // CLS
                    0xEE => {
                        // RET
                        self.registers.sp -= 1;
                        self.registers.pc = self.stack[self.registers.sp as usize];
                    }
                    _ => return Err(format!("Unknown 0x0 instruction: {:04X}", instruction)),
                }
            }
            0x1 => self.registers.pc = nnn, // JP addr
            0x2 => {
                // CALL addr
                self.stack[self.registers.sp as usize] = self.registers.pc;
                self.registers.sp += 1;
                self.registers.pc = nnn;
            }
            0x3 => {
                // SE Vx, byte
                if self.registers.get_v(x) == nn {
                    self.registers.pc += 2;
                }
            }
            0x4 => {
                // SNE Vx, byte
                if self.registers.get_v(x) != nn {
                    self.registers.pc += 2;
                }
            }
            0x5 => {
                // SE Vx, Vy
                if self.registers.get_v(x) == self.registers.get_v(y) {
                    self.registers.pc += 2;
                }
            }
            0x6 => self.registers.set_v(x, nn), // LD Vx, byte
            0x7 => {
                // ADD Vx, byte
                let result = self.registers.get_v(x).wrapping_add(nn);
                self.registers.set_v(x, result);
            }
            0x8 => {
                match n {
                    0x0 => self.registers.set_v(x, self.registers.get_v(y)), // LD Vx, Vy
                    0x1 => {
                        // OR Vx, Vy
                        let result = self.registers.get_v(x) | self.registers.get_v(y);
                        self.registers.set_v(x, result);
                    }
                    0x2 => {
                        // AND Vx, Vy
                        let result = self.registers.get_v(x) & self.registers.get_v(y);
                        self.registers.set_v(x, result);
                    }
                    0x3 => {
                        // XOR Vx, Vy
                        let result = self.registers.get_v(x) ^ self.registers.get_v(y);
                        self.registers.set_v(x, result);
                    }
                    0x4 => {
                        // ADD Vx, Vy
                        let vx = self.registers.get_v(x) as u16;
                        let vy = self.registers.get_v(y) as u16;
                        let result = vx + vy;
                        self.registers.set_v(0xF, if result > 255 { 1 } else { 0 });
                        self.registers.set_v(x, result as u8);
                    }
                    0x5 => {
                        // SUB Vx, Vy
                        let vx = self.registers.get_v(x);
                        let vy = self.registers.get_v(y);
                        self.registers.set_v(0xF, if vx > vy { 1 } else { 0 });
                        self.registers.set_v(x, vx.wrapping_sub(vy));
                    }
                    0x6 => {
                        // SHR Vx {, Vy}
                        let vx = self.registers.get_v(x);
                        self.registers.set_v(0xF, vx & 0x1);
                        self.registers.set_v(x, vx >> 1);
                    }
                    0x7 => {
                        // SUBN Vx, Vy
                        let vx = self.registers.get_v(x);
                        let vy = self.registers.get_v(y);
                        self.registers.set_v(0xF, if vy > vx { 1 } else { 0 });
                        self.registers.set_v(x, vy.wrapping_sub(vx));
                    }
                    0xE => {
                        // SHL Vx {, Vy}
                        let vx = self.registers.get_v(x);
                        self.registers.set_v(0xF, (vx & 0x80) >> 7);
                        self.registers.set_v(x, vx << 1);
                    }
                    _ => return Err(format!("Unknown 0x8 instruction: {:04X}", instruction)),
                }
            }
            0x9 => {
                // SNE Vx, Vy
                if self.registers.get_v(x) != self.registers.get_v(y) {
                    self.registers.pc += 2;
                }
            }
            0xA => self.registers.i = nnn, // LD I, addr
            0xB => self.registers.pc = nnn + self.registers.get_v(0) as u16, // JP V0, addr
            0xC => {
                // RND Vx, byte
                use std::collections::hash_map::DefaultHasher;
                use std::hash::{Hash, Hasher};
                let mut hasher = DefaultHasher::new();
                std::time::SystemTime::now().hash(&mut hasher);
                let random = (hasher.finish() & 0xFF) as u8;
                self.registers.set_v(x, random & nn);
            }
            0xD => {
                // DRW Vx, Vy, nibble
                let vx = self.registers.get_v(x);
                let vy = self.registers.get_v(y);
                let mut sprite = Vec::new();
                for i in 0..n {
                    sprite.push(self.memory.read_byte(self.registers.i + i as u16));
                }
                let collision = self.display.draw_sprite(vx, vy, &sprite);
                self.registers.set_v(0xF, if collision { 1 } else { 0 });
            }
            0xE => {
                match nn {
                    0x9E => {
                        // SKP Vx
                        if self.keyboard.is_key_pressed(self.registers.get_v(x) as usize) {
                            self.registers.pc += 2;
                        }
                    }
                    0xA1 => {
                        // SKNP Vx
                        if !self.keyboard.is_key_pressed(self.registers.get_v(x) as usize) {
                            self.registers.pc += 2;
                        }
                    }
                    _ => return Err(format!("Unknown 0xE instruction: {:04X}", instruction)),
                }
            }
            0xF => {
                match nn {
                    0x07 => self.registers.set_v(x, self.registers.delay_timer), // LD Vx, DT
                    0x0A => {
                        // LD Vx, K (wait for key press)
                        if let Some(key) = self.keyboard.get_pressed_key() {
                            self.registers.set_v(x, key as u8);
                        } else {
                            self.registers.pc -= 2; // Repeat this instruction
                        }
                    }
                    0x15 => self.registers.delay_timer = self.registers.get_v(x), // LD DT, Vx
                    0x18 => self.registers.sound_timer = self.registers.get_v(x), // LD ST, Vx
                    0x1E => {
                        // ADD I, Vx
                        self.registers.i = self.registers.i.wrapping_add(self.registers.get_v(x) as u16);
                    }
                    0x29 => {
                        // LD F, Vx (set I to location of sprite for digit Vx)
                        self.registers.i = 0x50 + (self.registers.get_v(x) as u16) * 5;
                    }
                    0x33 => {
                        // LD B, Vx (store BCD representation of Vx)
                        let vx = self.registers.get_v(x);
                        self.memory.write_byte(self.registers.i, vx / 100);
                        self.memory.write_byte(self.registers.i + 1, (vx / 10) % 10);
                        self.memory.write_byte(self.registers.i + 2, vx % 10);
                    }
                    0x55 => {
                        // LD [I], Vx (store registers V0 through Vx in memory starting at I)
                        for i in 0..=x {
                            self.memory.write_byte(self.registers.i + i as u16, self.registers.get_v(i));
                        }
                    }
                    0x65 => {
                        // LD Vx, [I] (read registers V0 through Vx from memory starting at I)
                        for i in 0..=x {
                            let value = self.memory.read_byte(self.registers.i + i as u16);
                            self.registers.set_v(i, value);
                        }
                    }
                    _ => return Err(format!("Unknown 0xF instruction: {:04X}", instruction)),
                }
            }
            _ => return Err(format!("Unknown instruction: {:04X}", instruction)),
        }
        Ok(())
    }
}
