struct Memory {
    memory: [u8; 4096],
}

impl Memory {
    pub fn new() -> Memory {
        Memory {
            memory: [0; 4096],
        }
    }

    pub fn read_byte(&self, address: u16) -> u8 {
        self.memory[address as usize]
    }

    pub fn write_byte(&mut self, address: u16, value: u8) {
        self.memory[address as usize] = value;
    }

    pub fn read_word(&self, address: u16) -> u16 {
        let low_byte = self.memory[address as usize];
        let high_byte = self.memory[address as usize + 1];
        (high_byte as u16) << 8 | low_byte as u16
    }

    pub fn load_program(&mut self, program: &[u8], start_address: u16) {
        let start = start_address as usize;
        let end = start + program.len();
        self.memory[start..end].copy_from_slice(program);
    }
}

