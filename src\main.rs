mod cpu;
mod display;
mod keyboard;
mod memory;
mod registers;

use cpu::Cpu;
use std::env;
use std::fs;

fn main() {
    let args: Vec<String> = env::args().collect();

    if args.len() != 2 {
        eprintln!("Usage: {} <rom_file>", args[0]);
        eprintln!("Example: {} roms/pong.ch8", args[0]);
        std::process::exit(1);
    }

    let rom_path = &args[1];

    // Load ROM file
    let rom_data = match fs::read(rom_path) {
        Ok(data) => data,
        Err(e) => {
            eprintln!("Error reading ROM file '{}': {}", rom_path, e);
            std::process::exit(1);
        }
    };

    // Create and initialize CPU
    let mut cpu = match Cpu::new() {
        Ok(cpu) => cpu,
        Err(e) => {
            eprintln!("Error initializing CPU: {}", e);
            std::process::exit(1);
        }
    };

    // Load ROM into memory
    cpu.load_rom(&rom_data);

    println!("Loaded ROM: {} ({} bytes)", rom_path, rom_data.len());
    println!("Starting CHIP-8 emulator...");
    println!("Controls:");
    println!("  CHIP-8 Keypad    Computer Keyboard");
    println!("  1 2 3 C          1 2 3 4");
    println!("  4 5 6 D          Q W E R");
    println!("  7 8 9 E          A S D F");
    println!("  A 0 B F          Z X C V");

    // Run the emulator
    if let Err(e) = cpu.run() {
        eprintln!("Emulator error: {}", e);
        std::process::exit(1);
    }
}
