struct Keyboard {
    keys: [bool; 16],
}

impl Keyboard {
    pub fn new() -> Self {
        Keyboard {
            keys: [false; 16],
        }
    }

    pub fn press_key(&mut self, key: usize) {
        if key < 16 {
            self.keys[key] = true;
        }
    }

    pub fn release_key(&mut self, key: usize) {
        if key < 16 {
            self.keys[key] = false;
        }
    }

    pub fn is_key_pressed(&self, key: usize) -> bool {
        if key < 16 {
            self.keys[key]
        } else {
            false
        }
    }

    pub fn get_pressed_key(&self) -> Option<usize> {
        self.keys.iter().position(|&key| key)
    }
}