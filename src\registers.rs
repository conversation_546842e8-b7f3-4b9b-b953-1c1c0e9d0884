struct Registers {
    v: [u8; 16],
    i: u16,
    pc: u16,
    sp: u8,
    delay_timer: u8,
    sound_timer: u8,
}

impl Registers {
    pub fn new() -> Self {
        Registers {
            v: [0; 16],
            i: 0,
            pc: 0x200,
            sp: 0,
            delay_timer: 0,
            sound_timer: 0,
        }
    }

    fn get_v(&self, index: usize) -> u8 {
        self.v[index]
    }

    fn set_v(&mut self, index: usize, value: u8) {
        self.v[index] = value;
    }

    fn update_timers(&mut self) {
        if self.delay_timer > 0 {
            self.delay_timer -= 1;
        }

        if self.sound_timer > 0 {
            self.sound_timer -= 1;
        }
    }
}