# Rusty CHIP-8 Emulator

A CHIP-8 emulator written in Rust using the `minifb` crate for graphics and input.

## Features

✅ **Fully Implemented:**
- Complete CHIP-8 CPU with all 35 instructions
- 64x32 pixel display with proper scaling
- 16-key hexadecimal keypad input
- 4KB memory management
- Font set loading (built-in hexadecimal digits 0-F)
- Timers (delay and sound)
- Stack for subroutines
- Collision detection for sprites
- Proper XOR-based sprite drawing

## Controls

The CHIP-8 keypad is mapped to your computer keyboard as follows:

```
CHIP-8 Keypad    Computer Keyboard
1 2 3 C          1 2 3 4
4 5 6 D          Q W E R
7 8 9 E          A S D F
A 0 B F          Z X C V
```

## Usage

1. **Build the emulator:**
   ```bash
   cargo build --release
   ```

2. **Run with a ROM file:**
   ```bash
   cargo run <rom_file>
   # Example:
   cargo run roms/test.ch8
   ```

3. **Test with the included test ROM:**
   ```bash
   python create_test_rom.py  # Creates roms/test.ch8
   cargo run roms/test.ch8
   ```

## Project Structure

- `src/main.rs` - Entry point and command-line interface
- `src/cpu.rs` - Main CPU implementation with instruction execution
- `src/display.rs` - Graphics display and window management
- `src/memory.rs` - Memory management (4KB RAM)
- `src/registers.rs` - CPU registers and timers
- `src/keyboard.rs` - Input handling
- `src/instructions.rs` - (Currently unused, legacy structure)

## CHIP-8 Instruction Set

The emulator implements all 35 CHIP-8 instructions:

### System Instructions
- `00E0` - Clear display
- `00EE` - Return from subroutine
- `1nnn` - Jump to address nnn
- `2nnn` - Call subroutine at nnn

### Register Operations
- `3xkk` - Skip next instruction if Vx = kk
- `4xkk` - Skip next instruction if Vx != kk
- `5xy0` - Skip next instruction if Vx = Vy
- `6xkk` - Set Vx = kk
- `7xkk` - Set Vx = Vx + kk
- `8xy0` - Set Vx = Vy
- `8xy1` - Set Vx = Vx OR Vy
- `8xy2` - Set Vx = Vx AND Vy
- `8xy3` - Set Vx = Vx XOR Vy
- `8xy4` - Set Vx = Vx + Vy, set VF = carry
- `8xy5` - Set Vx = Vx - Vy, set VF = NOT borrow
- `8xy6` - Set Vx = Vx SHR 1
- `8xy7` - Set Vx = Vy - Vx, set VF = NOT borrow
- `8xyE` - Set Vx = Vx SHL 1
- `9xy0` - Skip next instruction if Vx != Vy

### Memory Operations
- `Annn` - Set I = nnn
- `Bnnn` - Jump to location nnn + V0
- `Cxkk` - Set Vx = random byte AND kk
- `Dxyn` - Display n-byte sprite starting at memory location I at (Vx, Vy), set VF = collision

### Input Operations
- `Ex9E` - Skip next instruction if key with the value of Vx is pressed
- `ExA1` - Skip next instruction if key with the value of Vx is not pressed
- `Fx0A` - Wait for a key press, store the value of the key in Vx

### Timer Operations
- `Fx07` - Set Vx = delay timer value
- `Fx15` - Set delay timer = Vx
- `Fx18` - Set sound timer = Vx

### Memory Operations
- `Fx1E` - Set I = I + Vx
- `Fx29` - Set I = location of sprite for digit Vx
- `Fx33` - Store BCD representation of Vx in memory locations I, I+1, and I+2
- `Fx55` - Store registers V0 through Vx in memory starting at location I
- `Fx65` - Read registers V0 through Vx from memory starting at location I

## What Was Missing and How It Was Implemented

### 1. **Main CPU Core** (Completely Missing)
**What was missing:** No central CPU or emulation loop
**How implemented:** Created `src/cpu.rs` with:
- Main emulation loop with fetch-decode-execute cycle
- Complete instruction decoder for all 35 CHIP-8 instructions
- Timer management (60Hz updates)
- Integration of all components (memory, display, keyboard, registers)

### 2. **Module Visibility** (Structural Issue)
**What was missing:** All structs were private, preventing inter-module communication
**How implemented:** Made all main structs and methods public:
- `pub struct` declarations for Memory, Registers, Keyboard
- `pub fn` for all necessary methods

### 3. **Main Application** (Basic Stub)
**What was missing:** main.rs only had "Hello, world!"
**How implemented:** Complete application with:
- Command-line argument parsing for ROM files
- Error handling for file loading and emulator initialization
- User-friendly control instructions

### 4. **Font Set Loading** (Missing)
**What was missing:** No built-in font data for hexadecimal digits
**How implemented:** Added font set loading in CPU initialization with standard CHIP-8 font data

### 5. **Instruction Implementation** (Empty)
**What was missing:** instructions.rs was just an empty struct
**How implemented:** Moved all instruction logic into cpu.rs with complete implementation

## Testing

The project includes a test ROM generator (`create_test_rom.py`) that creates a simple program displaying smiley faces. This tests:
- Display clearing
- Sprite loading and drawing
- Memory addressing
- Basic program flow

## Dependencies

- `minifb` - Cross-platform window creation and pixel buffer rendering
- Standard Rust library for file I/O and system operations

## Future Enhancements

Potential improvements that could be added:
1. **Sound support** - Currently sound timer decrements but produces no audio
2. **Configurable controls** - Allow custom key mapping
3. **Save states** - Ability to save/load emulator state
4. **Debugger** - Step-through debugging with register/memory inspection
5. **ROM compatibility** - Testing with more CHIP-8 games and demos
6. **Performance optimization** - More efficient rendering and timing

The emulator is now fully functional and can run CHIP-8 programs!
