#!/usr/bin/env python3
"""
Simple script to create a test CHIP-8 ROM that displays a smiley face
"""

def create_test_rom():
    # Simple CHIP-8 program that displays a smiley face
    program = [
        # Clear screen
        0x00, 0xE0,

        # Set I to sprite data location (0x220)
        0xA2, 0x20,  # LD I, 0x220

        # Draw sprite at position (10, 10) with height 8
        0x61, 0x0A,  # LD V1, 10 (x position)
        0x62, 0x0A,  # LD V2, 10 (y position)
        0xD1, 0x28,  # DRW V1, V2, 8

        # Draw another sprite at position (30, 10)
        0x61, 0x1E,  # LD V1, 30
        0xD1, 0x28,  # DRW V1, V2, 8

        # Infinite loop
        0x12, 0x10,  # JP 0x210 (jump to infinite loop)

        # Infinite loop target
        0x12, 0x10,  # JP 0x210 (infinite loop)

        # Padding to reach sprite data location
    ] + [0x00] * (0x20 - 18) + [
        # Smiley face sprite data (8x8 pixels) at 0x220
        0x3C,  # 00111100
        0x42,  # 01000010
        0xA5,  # 10100101
        0x81,  # 10000001
        0xA5,  # 10100101
        0x99,  # 10011001
        0x42,  # 01000010
        0x3C,  # 00111100
    ]

    return bytes(program)

if __name__ == "__main__":
    rom_data = create_test_rom()
    with open("roms/test.ch8", "wb") as f:
        f.write(rom_data)
    print(f"Created test ROM: roms/test.ch8 ({len(rom_data)} bytes)")
